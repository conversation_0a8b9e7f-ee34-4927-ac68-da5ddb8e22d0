import threading
import time
import atexit
from functools import wraps

def batch_events(max_events, max_interval):
    def decorator(fn):
        batch = []
        lock = threading.Lock()
        timer = None

        def flush():
            nonlocal batch, timer
            # grab and clear the batch under lock…
            with lock:
                to_process = list(batch)
                batch.clear()
                timer = None

            # …then call the user function outside the lock
            if to_process:
                fn(to_process)

        @wraps(fn)
        def wrapper(event):
            nonlocal timer
            should_flush = False

            # only hold the lock for append+timer logic
            with lock:
                batch.append(event)

                if len(batch) >= max_events:
                    should_flush = True
                elif timer is None:
                    timer = threading.Timer(max_interval, flush)
                    timer.daemon = True
                    timer.start()

            # flush _after_ releasing the lock to avoid deadlock
            if should_flush:
                flush()

        # expose a manual flush and auto-flush on exit
        wrapper.flush = flush
        atexit.register(flush)
        return wrapper
    return decorator


if __name__ == "__main__":
    @batch_events(max_events=3, max_interval=5.0)
    def process_batch(batch):
        print(f"{time.strftime('%X')} Processing batch: {batch}")

    # emit 8 events, 1 per second
    for i in range(8):
        process_batch(f"evt{i}")
        time.sleep(1)

    # manually flush any leftovers
    process_batch.flush()
