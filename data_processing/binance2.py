import asyncio, json, sys
from collections import deque
from datetime import datetime
from sortedcontainers import SortedList  # or your two‐heap class
import websockets

WINDOW = 50
uri = 'wss://data-stream.binance.vision/ws/btcusdt@trade'

class FastRolling:
    def __init__(self, k):
        self.k = k
        self.buf = deque()
        self.sum = 0.0
        self.maxdq = deque()
        self.sorted = SortedList()

    def add(self, price):
        # add
        self.buf.append(price)
        self.sum += price
        while self.maxdq and self.maxdq[-1] < price:
            self.maxdq.pop()
        self.maxdq.append(price)
        self.sorted.add(price)
        # evict
        if len(self.buf) > self.k:
            old = self.buf.popleft()
            self.sum -= old
            if self.maxdq[0] == old:
                self.maxdq.popleft()
            self.sorted.remove(old)

    def mean(self):    return self.sum / len(self.buf)
    def maximum(self):return self.maxdq[0]
    def median(self):
        n = len(self.sorted)
        mid = n//2
        return self.sorted[mid] if n%2 else (self.sorted[mid-1]+self.sorted[mid])/2

import heapq
from collections import deque

class FastRollingTwoHeaps:
    def __init__(self, k):
        self.k = k
        self.buf = deque()              # holds (value, idx)
        self.sum = 0.0
        self.maxdq = deque()            # for O(1) max: holds (value, idx)

        # two heaps for median
        #   small: max-heap via (-value, idx)
        #   large: min-heap via ( value, idx)
        self.small, self.large = [], []
        self.small_size = 0
        self.large_size = 0

        # lazy-deletion set of expired idxs
        self.delayed = set()

        # unique counter for each tick
        self._idx = 0

    def prune(self, heap):
        # drop top entries whose idx is in delayed
        while heap and heap[0][1] in self.delayed:
            _, idx = heapq.heappop(heap)
            self.delayed.remove(idx)

    def rebalance(self):
        # ensure sizes differ by at most 1
        if self.small_size > self.large_size + 1:
            val, idx = heapq.heappop(self.small)
            heapq.heappush(self.large, (-val, idx))
            self.small_size -= 1
            self.large_size += 1
        elif self.large_size > self.small_size:
            val, idx = heapq.heappop(self.large)
            heapq.heappush(self.small, (-val, idx))
            self.large_size -= 1
            self.small_size += 1

    def add(self, value: float):
        idx = self._idx
        self._idx += 1

        # 1) push into buffer & sum
        self.buf.append((value, idx))
        self.sum += value

        # 2) update max‐deque
        while self.maxdq and self.maxdq[-1][0] < value:
            self.maxdq.pop()
        self.maxdq.append((value, idx))

        # 3) insert into appropriate heap
        if not self.small or value <= -self.small[0][0]:
            heapq.heappush(self.small, (-value, idx))
            self.small_size += 1
        else:
            heapq.heappush(self.large, (value, idx))
            self.large_size += 1

        # 4) evict oldest if over capacity
        if len(self.buf) > self.k:
            old_val, old_idx = self.buf.popleft()
            self.sum -= old_val

            # mark for lazy deletion
            self.delayed.add(old_idx)

            # adjust heap-size counters
            if old_val <= -self.small[0][0]:
                self.small_size -= 1
            else:
                self.large_size -= 1

            # clean max-deque head if it was evicted
            if self.maxdq and self.maxdq[0][1] == old_idx:
                self.maxdq.popleft()

        # 5) prune any delayed entries at heap tops, then rebalance
        self.prune(self.small)
        self.prune(self.large)
        self.rebalance()

    def mean(self) -> float:
        return self.sum / len(self.buf)

    def maximum(self) -> float:
        return self.maxdq[0][0]

    def median(self) -> float:
        # ensure top elements are live
        self.prune(self.small)
        self.prune(self.large)

        # odd → extra element in small
        if self.small_size > self.large_size:
            return -self.small[0][0]
        # even → average two middles
        return (-self.small[0][0] + self.large[0][0]) / 2



async def main():
    # stats = FastRolling(WINDOW)
    stats = FastRollingTwoHeaps(WINDOW)
    async with websockets.connect(uri) as ws:
        async for msg in ws:
            t = json.loads(msg)
            p = float(t['p'])
            q = float(t['q'])
            dt = datetime.fromtimestamp(t['T']/1000).strftime("%H:%M:%S.%f")[:-3]

            stats.add(p)
            line = (
                f"{dt}  price={p:,.2f}  qty={q:.6f}  "
                f"max={stats.maximum():,.2f}  "
                f"mean={stats.mean():,.2f}  "
                f"med={stats.median():,.2f}"
            )
            # overwrite the same line
            sys.stdout.write(line + "\r")
            # flush only if needed:
            # sys.stdout.flush()

if __name__ == "__main__":
    asyncio.run(main())
