"""
Concurrent Tensor Dataloader for simulated data
"""

from time import sleep
import torch
from torch.utils.data import Dataset, DataLoader
from threading import Thread, Event
from queue import Queue
from concurrent.futures import ThreadPoolExecutor, as_completed
from heapq import heappush, heappop
from typing import Iterator, Callable, List, Union



class FileTensorDataset(DataLoader):
    def __init__(self, file_list: list[str], load_fn: callable):
        self.file_list = file_list
        self.load_fn = load_fn
    

    def __len__(self):
        return len(self.file_list)

    def __getitem__(self, idx):
        return self.load_fn(self.file_list[idx])


class BatchedOrderedStreamingPrefetchLoader:
    def __init__(
            self,
            dataset: Dataset,
            buffer_size: int,
            batch_size: int,
            stack: bool = True,
            max_workers: int = 4, 
    ):
        self.dataset = dataset
        self.buffer_size = buffer_size
        self.batch_size = batch_size
        self.stack = stack 
        self._stop_event = Event()
        self._max_workers = max_workers
    
    def __iter__(self):
        stop_event = self._stop_event
        result_q = Queue(maxsize=self.buffer_size)
        heap = []

        def loader():
            with ThreadPoolExecutor(max_workers=self._max_workers) as executor:
                futures = {
                    executor.submit(lambda i=i: (i, self.dataset[i])): i for i in range(len(self.dataset))
                }

                for future in as_completed(futures):
                    if stop_event.is_set():
                        break
                    
                    try:
                        idx, data = future.result()
                        result_q.put((idx, data))
                    except Exception as e:
                        print(f"[Loader] Error: {e}")
                    
                result_q.put(None)

        thread = Thread(target=loader)
        thread.start()

        expected_idx = 0
        batch = []

        try:
            while True:
                item = result_q.get()
                if item is None:
                    break
                
                idx, tensor = item
                if idx == expected_idx:
                    batch.append(tensor)
                    expected_idx += 1
                else:
                    heappush(heap, (idx, tensor))
                
                while heap and heap[0][0] == expected_idx:
                    _, tensor = heappop(heap)
                    batch.append(tensor)
                    expected_idx += 1
                
                # yield full batch
                if len(batch) == self.batch_size:
                    yield torch.stack(batch) if self.stack else batch
                    batch = []
                
            # Drain remaining 
            while heap:
                _, tensor = heappop(heap)
                batch.append(tensor)
                expected_idx += 1
                if len(batch) == self.batch_size:
                    yield torch.stack(batch) if self.stack else batch
                    batch = []
        

            # last batch
            if batch:
                yield torch.stack(batch) if self.stack else batch

        finally:
            print("[Loader] Exiting upon finishing generator")
            # yield rest of batch from result_q and heap

            stop_event.set()
            thread.join()

def load_fn(i):
    print(f"loading {i=}")
    sleep(i * 0.1)
    return torch.ones(3,3) * (i + 1)


if __name__ == "__main__":
    dataset = FileTensorDataset(list(range(10)), load_fn)
    loader = BatchedOrderedStreamingPrefetchLoader(dataset, buffer_size=2, batch_size=2, stack=False)
    
    for i, batch in enumerate(loader):
        if i == 5:
            break
        print(f"{i, batch=}")
    
        

