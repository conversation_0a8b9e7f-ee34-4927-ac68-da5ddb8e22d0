import gc
import inspect

# -----------------------------------------------------------------------------
# Demonstrate exactly which Tracked objects are freed by the GC cycle detector.
# Only cycles (not plain refcount) end up in our “removed” list in the GC callback.
# -----------------------------------------------------------------------------

# Global registry of all Tracked instances by id → (name, trace)
_tracked_objs = {}

# Buffer for per‐cycle GC events
_gc_events = []

def _gc_callback(phase, info):
    gen = info.get('generation', '?')
    counts = gc.get_count()
    # get set of live tracked‐ids before/after GC pass
    live_ids = {id(o) for o in gc.get_objects() if id(o) in _tracked_objs}
    if phase == 'start':
        _gc_events.append({
            'phase': 'START',
            'gen': gen,
            'counts_before': counts,
            'start_ids': live_ids,
            'removed': []
        })
    elif phase == 'stop':
        entry = _gc_events[-1]
        entry['counts_after'] = counts
        removed_ids = entry['start_ids'] - live_ids
        entry['removed'] = [ _tracked_objs[i] for i in removed_ids ]

class Tracked:
    def __init__(self, name):
        # capture creation site
        frm = inspect.currentframe().f_back
        info = inspect.getframeinfo(frm)
        trace = f"{info.filename}:{info.lineno}"
        self._name = name
        self._trace = trace
        # register
        _tracked_objs[id(self)] = (self._name, self._trace)
    def __repr__(self):
        return f"<Tracked {self._name} at {self._trace}>"

def main():
    # 1) hook GC and use very low thresholds
    gc.callbacks.append(_gc_callback)
    gc.set_threshold(3,2,1)

    print("Thresholds:", gc.get_threshold(), "Counts:", gc.get_count())

    # 2) create purely‐cyclic objects so only GC can collect them
    print("\n-- Creating self‐cycles --")
    for i in range(5):
        t = Tracked(name=f"t{i}")
        # create a 2‐object (self) cycle:
        t.partner = t
        # drop our only strong ref:
        del t

    # 3) force collection
    print("\n-- Forcing GC --")
    gc.collect()

    # 4) teardown
    gc.callbacks.remove(_gc_callback)

    # 5) report
    print("\n=== GC Events & Collected Objects ===")
    for e in _gc_events:
        print(f"{e['phase']} gen={e['gen']}  before={e['counts_before']}  after={e['counts_after']}")
        if e['removed']:
            for name, trace in e['removed']:
                print(f"   • Collected: {name} created at {trace}")
        else:
            print("   • No tracked objects collected")

if __name__ == "__main__":
    main()
