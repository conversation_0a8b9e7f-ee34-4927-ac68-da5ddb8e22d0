import gzip
import re
from collections import Counter, defaultdict
from urllib.request import urlopen

LOG_URL = "file:///Users/<USER>/Documents/code/sys/data_processing/data/access.log.gz"
LINE_RE = re.compile(r'^(?P<ip>\S+) .*?"\S+ (?P<path>\S+)')


def stream_lines(url):
    with urlopen(url) as resp, gzip.GzipFile(fileobj=resp) as gf:
        for raw in gf:
            yield raw.decode("utf-8", errors="ignore")


def aggreate():
    ip_counts = Counter()
    path_total = defaultdict(list)  # path -> list of response times

    for line in stream_lines(LOG_URL):
        m = LINE_RE.match(line)
        if not m:
            continue
        ip, path = m.group("ip"), m.group("path")
        ip_counts[ip] += 1

        # Simulate extracting response time from the last field
        resp_time = float(line.strip().split()[-1]) / 1000.0
        path_total[path].append(resp_time)

    return ip_counts, path_total


if __name__ == "__main__":
    ip_counts, path_total = aggreate()

    # Top 5 IPs by request count
    for ip, cnt in ip_counts.most_common(5):
        print(f"{ip}: {cnt} requests")

    # Top 5 slowest endpoint by average response time

    avg_times = {path: sum(times) / len(times) for path, times in path_total.items()}
    slowest_five = sorted(avg_times.items(), key=lambda x: x[1], reverse=True)[:5]
    print("\nTop 5 Slowest endpoints:")
    for path, avg in slowest_five:
        print(f"    {path}: {avg:.3f}s")
