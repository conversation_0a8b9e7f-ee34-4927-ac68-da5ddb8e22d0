{"cells": [{"cell_type": "code", "execution_count": 1, "id": "dc900e15", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thresholds set to: (700, 10, 10)\n", "Initial counts: (152, 2, 6)\n"]}], "source": ["import gc\n", "\n", "print(\"Thresholds set to:\", gc.get_threshold())\n", "print(\"Initial counts:\", gc.get_count())"]}, {"cell_type": "code", "execution_count": null, "id": "06aa4e49", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "torch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}