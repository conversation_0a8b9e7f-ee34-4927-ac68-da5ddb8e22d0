from dataclasses import dataclass
from typing import List, Optional, Protocol


BLOCK_SIZE = 8


class Node:
    def __init__(self, *args: List[str]):
        joined = "".join(args)
        if len(joined) != BLOCK_SIZE:
            raise ValueError(f"Node must be {BLOCK_SIZE} bytes")
        self.data = joined

    def __str__(self):
        return f"[{','.join(self.data)}]"


class FileInterface(Protocol):
    def write(self, pos: int, data: str) -> None: ...
    def read(self, pos: int, size: str) -> str: ...
    def print(self) -> None: ...


class File:
    def __init__(self, name: str, fs: "FileSystem"):
        self.name = name
        self.fs = fs
        self.block_indices: List[int] = []

    def write(self, pos: int, data: str) -> None:
        assert len(data) % 8 == 0 and pos % 8 == 0
        block_index = pos // 8
        if block_index >= len(self.block_indices):
            self.block_indices.append(self.fs.allocate_block())
        self.fs.write(self.block_indices[block_index], data)

    def read(self, pos: int, size: int) -> str:
        assert size % 8 == 0 and pos % 8 == 0
        block_index = pos // 8
        if block_index >= len(self.block_indices):
            return ""
class FileSystem: ...
