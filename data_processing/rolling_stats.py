import pandas as pd

CSV_URL = "data/prices.feather"

def main():
    # df = pd.read_csv(CSV_URL, parse_dates=["timestamp"])
    # df.set_index("timestamp", inplace=True)
    df = pd.read_parquet(CSV_URL)
    print(df.head(10))

    # 1-second rolling window 
    rolling = df["price"].rolling("5s")

    df["rolling_mean"] = rolling.mean()
    df["rolling_std"] = rolling.std() # volatility
    df["rolling_max"] = rolling.max()
    df["rolling_min"] = rolling.min()

    print(rolling)
    print(df.head(10))

    # output to feather
    df.to_parquet("data/prices.feather")

if __name__ == "__main__":
    main()
