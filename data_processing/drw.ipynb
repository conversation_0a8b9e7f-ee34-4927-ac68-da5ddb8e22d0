{"cells": [{"cell_type": "code", "execution_count": 1, "id": "360803aa", "metadata": {}, "outputs": [], "source": ["import gc "]}, {"cell_type": "code", "execution_count": 3, "id": "2dca51e4", "metadata": {}, "outputs": [{"data": {"text/plain": ["(700, 10, 10)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["gc.get_threshold()"]}, {"cell_type": "code", "execution_count": 7, "id": "19e381c3", "metadata": {}, "outputs": [{"data": {"text/plain": ["('CPython', 'cpython', '/Users/<USER>/Documents/code/sys/torch/bin/python')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["import platform, sys\n", "platform.python_implementation(), sys.implementation.name, sys.executable"]}, {"cell_type": "code", "execution_count": null, "id": "74c36e9b", "metadata": {}, "outputs": [{"data": {"text/plain": ["'3.11.11'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "platform.python_version()"]}, {"cell_type": "code", "execution_count": 11, "id": "2f99fd9d", "metadata": {}, "outputs": [{"data": {"text/plain": ["bytearray(b'adc')"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["bytearray(b\"adc\")"]}, {"cell_type": "code", "execution_count": 12, "id": "69b1939d", "metadata": {}, "outputs": [{"data": {"text/plain": ["(384, 2, 10)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["gc.get_count()"]}, {"cell_type": "code", "execution_count": 13, "id": "aee3c12b", "metadata": {}, "outputs": [{"data": {"text/plain": ["1037"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["gc.collect()"]}, {"cell_type": "code", "execution_count": 14, "id": "ba1f6ac5", "metadata": {}, "outputs": [{"data": {"text/plain": ["(558, 0, 0)"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["gc.get_count()"]}, {"cell_type": "code", "execution_count": 1, "id": "f51642ca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Initial counts: (153, 2, 6)\n"]}], "source": ["import gc\n", "print(\"Initial counts:\", gc.get_count())\n"]}, {"cell_type": "code", "execution_count": null, "id": "2d661d96", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "torch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}